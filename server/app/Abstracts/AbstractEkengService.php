<?php

namespace App\Abstracts;

use App\Exceptions\CitizenNotFoundException;
use App\Exceptions\InternalErrorException;
use App\Exceptions\InvalidDocumentException;
use App\Exceptions\InvalidSSNException;
use App\Exceptions\UnknownDocumentTypeException;
use App\Helpers\PassportHelper;
use App\Interfaces\IEkengService;
use App\Interfaces\IRedisService;
use App\Models\EkengRequest;
use App\Models\SettingsStatus;
use App\Services\ExternalJsonService;
use App\Traits\CheckEkengResponse;
use App\Traits\MeasureExecutionTime;
use App\Traits\StoreThirdPartyResponse;
use Exception;
use GuzzleHttp\Client;
use Log;

class AbstractEkengService extends ExternalJsonService implements IEkengService
{
    use CheckEkengResponse;
    use MeasureExecutionTime;
    use StoreThirdPartyResponse;

    private $client;
    private $token;
    private $secret;
    private $iv;
    private $redis_service;
    private $alert_service;

    public function __construct(IRedisService $redis_service)
    {
        $this->redis_service = $redis_service;
        $this->token = env('EKENG_TOKEN');
        $this->secret = env('EKENG_SECRET');
        $this->iv = env('EKENG_IV');
        $this->alert_service = resolve('App\Services\AlertService');
    }

    protected function getConnection()
    {
        if (!$this->client) {
            $this->client = new Client([
                'base_uri' => env('EKENG_URL'),
                'verify' => false,
            ]);
        }

        return $this->client;
    }

    public function getCitizen($document_type, $document_number)
    {
        try {
            Log::info('Getting citizen data from EKENG', ['document_number' => $document_number, 'document_type' => $document_type]);
            // If document type is 'passport' or 'id_card' we should get 'soc_card_number' by it, then request citizen data by 'soc_card_number'
            // We store ['passport_number' => 'soc_card_number'] and ['soc_card_number' => 'citizen_data'] and return 'citizen_data' from cache if exists
            if ($document_type == constants('SOC_CARD')) {
                $citizen = $this->getCitizenFromCacheOrEkeng($document_number);
            } elseif ($document_type == constants('ID_CARD') || $document_type == constants('PASSPORT')) {
                $passport_data = $this->redis_service->get("$document_number:ekeng");

                if (!$passport_data) {
                    $citizen = $this->getCitizenByPassport($document_number);
                } else {
                    $citizen = $this->getCitizenFromCacheOrEkeng($passport_data->PNum);
                }
            } else {
                throw new UnknownDocumentTypeException();
            }

            if ($citizen && isset($citizen['first-name'])) {
                $citizen['first_name'] = $citizen['first-name'];
            }

            return $citizen;
        } catch (UnknownDocumentTypeException $e) {
            Log::warning('AbstractEkengService getCitizen UnknownDocumentTypeException', ['error' => $e->getMessage()]);
            throw new InvalidDocumentException();
        } catch (InvalidSSNException $e) {
            Log::warning('AbstractEkengService getCitizen InvalidSSNException', ['error' => $e->getMessage()]);
            throw $e;
        } catch (CitizenNotFoundException | Exception $e) {
            Log::error('AbstractEkengService, '.get_class($e), ['error' => $e->getMessage()]);

            $citizen = $this->getCitizenFromDBAndProcessAlert($passport_data->PNum ?? $document_number, 'ALERT.ACTIONS.EKENG');
            if (!empty($citizen)) {
                return $citizen;
            }

            throw new InternalErrorException('Something went wrong', [], constants('ALERT.ACTIONS.EKENG'), null, true, []);
        }
    }

    protected function getCitizenFromCacheOrEkeng($ssn)
    {
        Log::debug('Getting EKENG data from cache', ['social_card_number' => $ssn]);
        $cached = $this->redis_service->get("$ssn:ekeng");

        if ($cached) {
            Log::debug('Return cached EKENG data');

            return $this->markCached($cached, true);
        }
        Log::debug('There are no EKENG data in cache');

        $citizen_ekeng = $this->getCitizenBySSN($ssn);

        return $this->markCached($citizen_ekeng);
    }

    private function getCitizenBySSN($ssn)
    {
        Log::info('Requesting citizen data from EKENG', ['social_card_number' => $ssn]);
        $this->isValidSSN($ssn);

        $opaque = str_random(env('OPAQUE_LENGTH'));
        $data = [
            'token' => $this->token,
            'opaque' => $opaque,
            'ssn' => $ssn,
        ];

        $client = $this->getConnection();

        $request_time = now();
        $response = $this->measureExecutionTime(function () use ($client, $data) {
            return $client->post('/authorize', ['form_params' => $data]);
        },
            __FUNCTION__,
            ['Data' => $data]
        );

        $body = $response->getBody();
        $res = json_decode($body->getContents());

        if (isset($res->data)) {
            $citizen = $this->decrypt($res->data);
            Log::debug('EKENG getCitizenBySSN response');

            $response_data = [
                'content' => $citizen,
                'identifier' => $ssn,
                'identifier_type' => constants('SOC_CARD'),
                'date' => $request_time,
            ];

            $this->storeThirdPartyResponse(new EkengRequest(), $response_data);

            $this->validate($citizen, constants('ALERT.ACTIONS.EKENG_SSN'));

            if ($citizen && $citizen->opaque == $opaque && !empty($citizen->passport_data)) {
                $this->isValidSSN($citizen->passport_data->PNum);
                $this->checkPoliceData($citizen);

                return $this->updateCache($ssn, $citizen);
            }
        }

        $citizen = $this->getCitizenFromDBAndProcessAlert($ssn, 'ALERT.ACTIONS.EKENG_SSN');
        if (!empty($citizen)) {
            return $citizen;
        }

        Log::error('Get citizen by SSN from EKENG, CitizenNotFoundException', ['response' => $res]);
        throw new CitizenNotFoundException('Citizen not found', constants('ALERT.ACTIONS.EKENG_SSN'), constants('ALERT.CASES.NO_DATA'), true, ['ssn' => $ssn ?? '']);
    }

    public function getCitizenByPassport($document_number)
    {
        Log::info('Requesting citizen data from EKENG', ['passport_number' => $document_number]);
        $opaque = str_random(env('OPAQUE_LENGTH'));
        $data = [
            'token' => $this->token,
            'opaque' => $opaque,
            'type' => 'doc_name',
            'documentNumber' => $document_number,
        ];

        $client = $this->getConnection();

        $request_time = now();
        $response = $this->measureExecutionTime(function () use ($client, $data) {
            return $client->post('/get-user', ['form_params' => $data]);
        },
            __FUNCTION__,
            ['Data' => $data]
        );

        $body = $response->getBody();
        $res = json_decode($body->getContents());

        if ($res->data) {
            $citizen = $this->decrypt($res->data);

            $response_data = [
                'content' => $citizen,
                'identifier' => $document_number,
                'identifier_type' => constants('PASSPORT'),
                'date' => $request_time,
            ];

            $this->storeThirdPartyResponse(new EkengRequest(), $response_data);

            $this->validate($citizen, constants('ALERT.ACTIONS.EKENG_PASSPORT'));

            Log::debug('EKENG getCitizenByPassport response');

            if ($citizen && $citizen->data && $citizen->opaque == $opaque) {
                Log::debug('Storing citizen data from EKENG in cache with key', ['passport_number' => $document_number]);
                $this->redis_service->update("$document_number:ekeng", $citizen->data, constants('THIRTY_MINUTES'));
                Log::debug('Citizen data from EKENG stored in cache with key', ['passport_number' => $document_number]);

                return $this->getCitizenFromCacheOrEkeng($citizen->data->PNum);
            }
        }

        $citizen = $this->getCitizenFromDBAndProcessAlert($document_number, 'ALERT.ACTIONS.EKENG_PASSPORT');
        if (!empty($citizen)) {
            return $citizen;
        }

        Log::error('Get citizen by Passport from EKENG, CitizenNotFoundException', ['response' => $res]);
        throw new CitizenNotFoundException('Citizen not found', constants('ALERT.ACTIONS.EKENG_PASSPORT'), constants('ALERT.CASES.NO_DATA'), true, ['document_number' => $document_number ?? '']);
    }

    protected function decrypt($data)
    {
        Log::debug('Decrypting response from EKENG');
        $decrypted = openssl_decrypt($data, env('EKENG_DECRYPT'), $this->secret, null, base64_decode($this->iv));
        if ($decrypted) {
            Log::debug('Response from EKENG decrypted');

            return json_decode($decrypted);
        }
        Log::error('Can\'t decrypt response from EKENG');

        return false;
    }

    protected function updateCache($ssn, $citizen)
    {
        Log::debug('Storing citizen data from EKENG in cache with key', ['social_card_number' => $ssn]);
        $this->redis_service->update("$ssn:ekeng", (array) $citizen, constants('THIRTY_MINUTES'));
        Log::debug('Citizen data from EKENG stored in cache with key', ['social_card_number' => $ssn]);

        return (array) $citizen;
    }

    protected function checkPoliceData($citizen)
    {
        if (!$this->isComplete($citizen)) {
            $this->alert_service->processAlerting(
                constants('ALERT.ACTIONS.EKENG_NO_VEHICLE'),
                constants('ALERT.CASES.NO_VEHICLE_DATA'),
                ['ssn' => $citizen->SSN]
            );
        }
    }

    protected function isComplete($citizen)
    {
        return !is_null($citizen->vehicle_info) && $citizen->vehicle_info !== '';
    }

    protected function isValidSSN($ssn)
    {
        if (!PassportHelper::isSocialCard($ssn)) {
            Log::error('Get citizen by SSN from EKENG, Invalid SSN Exception', ['SSN' => $ssn]);

            throw new InvalidSSNException();
        }
    }

    public function cutEkengPhoto($citizen)
    {
        if (isset($citizen['ekeng']['passport_data']->Photo)) {
            $citizen['ekeng']['passport_data']->Photo = null;
        }

        if (isset($citizen['passport_data']->Photo)) {
            $citizen['passport_data']->Photo = null;
        }

        return $citizen;
    }

    private function getCitizenFromDBAndProcessAlert($identifier, $alert_action): ?array
    {
        $citizen = $this->getCitizenFromDB($identifier);
        if (!empty($citizen)) {
            Log::info('Got citizen from DB', ['identifier' => $identifier]);

            $this->alert_service->processAlerting(
                constants($alert_action),
                null,
                ['identifier' => $identifier]
            );

            return $citizen;
        }

        return null;
    }

    protected function getCitizenFromDB(string $identifier): array
    {
        $settings_service = resolve('App\Interfaces\ISettingsService');
        if ($settings_service->isGeneralSettingTypeDisabled(SettingsStatus::GET_EKENG_DATA_FROM_DB)) {
            return [];
        }

        try {
            // If identifier is not SSN, try to get SSN from passport data
            if (!PassportHelper::isSocialCard($identifier)) {
                $content = $this->getEkengRequestContentByType($identifier, 'PASSPORT');
                if ($content) {
                    $details = json_decode($content);
                    $this->cacheEkengDataFromDB($identifier, $details->data, 'passport_number');

                    $identifier = $details->data->PNum ?? null;
                }
            }

            // Get and cache data based on SSN identifier
            $content = $this->getEkengRequestContentByType($identifier, 'SOC_CARD');
            if ($content) {
                $content = json_decode($content);
                $this->cacheEkengDataFromDB($identifier, (array) $content, 'SSN');

                return (array) $content;
            }

            return [];
        } catch (Exception $e) {
            Log::error('Error retrieving citizen data from DB', [
                'identifier' => $identifier,
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    private function cacheEkengDataFromDB(string $identifier, $data, string $type): void
    {
        Log::debug('Storing citizen EKENG data from DB in cache', [
            'identifier' => $identifier,
            'type' => $type,
        ]);

        $this->redis_service->update(
            "$identifier:ekeng",
            $data,
            constants('THIRTY_MINUTES')
        );

        Log::debug('Citizen EKENG data from DB stored in cache', [
            'identifier' => $identifier,
            'type' => $type,
        ]);
    }

    private function getEkengRequestContent($identifier)
    {
        $request_date = now()->subMinutes(constants('GET_EKENG_INFO_TIME_BY_MINUTES'));

        return EkengRequest::where('identifier', $identifier)
            ->whereDate('date', '>=', $request_date)
            ->latest()
            ->value('content');
    }

    private function getEkengRequestContentByType($identifier, $type)
    {
        // Only process supported identifier types
        if (!in_array($type, ['PASSPORT', 'SOC_CARD'])) {
            return null;
        }

        $request_date = now()->subMinutes(constants('GET_EKENG_INFO_TIME_BY_MINUTES'));

        return EkengRequest::where('identifier_type', $type)
            ->where('identifier', $identifier)
            ->whereDate('date', '>=', $request_date)
            ->latest()
            ->value('content');
    }
}
